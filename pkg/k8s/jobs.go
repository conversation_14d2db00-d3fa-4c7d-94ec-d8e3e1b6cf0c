package k8s

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/mneme/db-tools/pkg/config"
	"github.com/mneme/db-tools/pkg/utils"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

const (
	awsRegion = "ca-central-1"
)

// sanitizeJobName converts database names to be RFC 1123 compliant for Kubernetes job names
// Replaces underscores with hyphens and converts to lowercase
func sanitizeJobName(dbName string) string {
	return strings.ToLower(strings.ReplaceAll(dbName, "_", "-"))
}

// unsanitizeJobName converts sanitized job names back to original database names
// Replaces hyphens with underscores (reverse of sanitizeJobName)
func unsanitizeJobName(sanitizedName string) string {
	return strings.ReplaceAll(sanitizedName, "-", "_")
}

// getEnvWithDefault returns the value of the environment variable or the default value if not set
func getEnvWithDefault(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// CreateBackupJob creates a Kubernetes job for database backup
func CreateBackupJob(k8sClient *kubernetes.Clientset, dbConfig config.BackupDatabaseConfig) (*batchv1.Job, error) {
	jobName := fmt.Sprintf("mneme-%s-%s", sanitizeJobName(dbConfig.Database), utils.GetTimestampInTimezone())
	log.Printf("Creating backup job: %s", jobName)

	// Get the parent pod's image
	parentImage := os.Getenv("HOSTNAME")
	if parentImage == "" {
		return nil, fmt.Errorf("unable to get parent pod name")
	}

	// Get the pod to find its image
	pod, err := k8sClient.CoreV1().Pods(os.Getenv("POD_NAMESPACE")).Get(context.TODO(), parentImage, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("unable to get parent pod: %v", err)
	}

	// Find the mneme container's image
	var image string
	for _, container := range pod.Spec.Containers {
		if container.Name == "mneme" {
			image = container.Image
			break
		}
	}
	if image == "" {
		return nil, fmt.Errorf("unable to find mneme container image in parent pod")
	}

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: os.Getenv("POD_NAMESPACE"),
		},
		Spec: batchv1.JobSpec{
			TTLSecondsAfterFinished: aws.Int32(1800), // Delete succeeded jobs after 30 minutes
			Template: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					ServiceAccountName: "mneme",
					Containers: []corev1.Container{
						{
							Name:            "mneme",
							Image:           image,
							ImagePullPolicy: corev1.PullPolicy("IfNotPresent"),
							Command:         []string{"/usr/local/bin/entrypoint.sh"},
							Args:            []string{"mneme", "-single"},
							Env: []corev1.EnvVar{
								{
									Name:  "AWS_REGION",
									Value: awsRegion,
								},
								{
									Name:  "DB_TYPE",
									Value: dbConfig.Type,
								},
								{
									Name:  "DB_HOST",
									Value: dbConfig.Host,
								},
								{
									Name:  "DB_PORT",
									Value: fmt.Sprintf("%d", dbConfig.Port),
								},
								{
									Name:  "DB_NAME",
									Value: dbConfig.Database,
								},
								{
									Name:  "DB_USERNAME",
									Value: dbConfig.Username,
								},
								{
									Name:  "DB_PASSWORD",
									Value: dbConfig.Password,
								},
								{
									Name:  "S3_BUCKET",
									Value: dbConfig.S3Bucket,
								},
								{
									Name:  "S3_PATH",
									Value: dbConfig.S3Path,
								},
								{
									Name: "POD_NAMESPACE",
									ValueFrom: &corev1.EnvVarSource{
										FieldRef: &corev1.ObjectFieldSelector{
											FieldPath: "metadata.namespace",
										},
									},
								},
							},
						},
					},
					RestartPolicy: corev1.RestartPolicyOnFailure,
				},
			},
		},
	}

	return k8sClient.BatchV1().Jobs(os.Getenv("POD_NAMESPACE")).Create(context.TODO(), job, metav1.CreateOptions{})
}

// CreateSanitizeJob creates a Kubernetes job for database restore and sanitization
func CreateSanitizeJob(k8sClient *kubernetes.Clientset, dbConfig config.RestoreDatabaseConfig) (*batchv1.Job, error) {
	jobName := fmt.Sprintf("db-sanitize-%s-%s", sanitizeJobName(dbConfig.Database), utils.GetTimestampInTimezone())
	log.Printf("Creating sanitize job: %s", jobName)

	// Get the parent pod's image
	parentImage := os.Getenv("HOSTNAME")
	if parentImage == "" {
		return nil, fmt.Errorf("unable to get parent pod name")
	}

	// Get the pod to find its image
	pod, err := k8sClient.CoreV1().Pods(os.Getenv("POD_NAMESPACE")).Get(context.TODO(), parentImage, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("unable to get parent pod: %v", err)
	}

	// Find the container's image
	var image string
	for _, container := range pod.Spec.Containers {
		if container.Name == "db-restore-sanitize" || container.Name == "mneme" {
			image = container.Image
			break
		}
	}
	if image == "" {
		return nil, fmt.Errorf("unable to find container image in parent pod")
	}

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: os.Getenv("POD_NAMESPACE"),
		},
		Spec: batchv1.JobSpec{
			TTLSecondsAfterFinished: aws.Int32(3600), // Delete succeeded jobs after 60 minutes
			Template: corev1.PodTemplateSpec{
				Spec: corev1.PodSpec{
					ServiceAccountName: "mneme",
					Containers: []corev1.Container{
						{
							Name:            "db-restore-sanitize",
							Image:           image,
							ImagePullPolicy: corev1.PullPolicy("IfNotPresent"),
							Command:         []string{"/usr/local/bin/entrypoint.sh"},
							Args:            []string{"db-restore-sanitize", "-single"},
							Env: []corev1.EnvVar{
								{
									Name:  "AWS_REGION",
									Value: awsRegion,
								},
								{
									Name:  "DB_TYPE",
									Value: dbConfig.Type,
								},
								{
									Name:  "DB_HOST",
									Value: dbConfig.Host,
								},
								{
									Name:  "DB_PORT",
									Value: fmt.Sprintf("%d", dbConfig.Port),
								},
								{
									Name:  "DB_NAME",
									Value: dbConfig.Database,
								},
								{
									Name:  "DB_USERNAME",
									Value: dbConfig.Username,
								},
								{
									Name:  "DB_PASSWORD",
									Value: dbConfig.Password,
								},
								{
									Name:  "S3_BUCKET",
									Value: dbConfig.S3Bucket,
								},
								{
									Name:  "S3_SOURCE_PATH",
									Value: dbConfig.S3SourcePath,
								},
								{
									Name:  "S3_DESTINATION_PATH",
									Value: dbConfig.S3DestinationPath,
								},
								{
									Name:  "BACKUP_FILE_NAME",
									Value: dbConfig.BackupFileName,
								},
								{
									Name:  "USE_LATEST_BACKUP",
									Value: fmt.Sprintf("%t", dbConfig.UseLatestBackup),
								},
								{
									Name:  "SANITIZED_FILE_PREFIX",
									Value: dbConfig.SanitizedFilePrefix,
								},
								// ECR sanitization configuration
								{
									Name:  "ECR_IMAGE",
									Value: dbConfig.ECRImage,
								},
								{
									Name:  "SANITIZE_COMMAND",
									Value: dbConfig.SanitizeCommand,
								},
								{
									Name:  "SANITIZE_ARGS",
									Value: strings.Join(dbConfig.SanitizeArgs, ","),
								},
								{
									Name: "POD_NAMESPACE",
									ValueFrom: &corev1.EnvVarSource{
										FieldRef: &corev1.ObjectFieldSelector{
											FieldPath: "metadata.namespace",
										},
									},
								},
								// Add root credentials for database creation
								{
									Name:  "MYSQL_ROOT_USERNAME",
									Value: "root",
								},
								{
									Name: "MYSQL_ROOT_PASSWORD",
									ValueFrom: &corev1.EnvVarSource{
										SecretKeyRef: &corev1.SecretKeySelector{
											LocalObjectReference: corev1.LocalObjectReference{
												Name: "db-credentials",
											},
											Key: "mysql-root-password",
										},
									},
								},
								// Add ARES_ECR_ENDPOINT for Ares sanitization
								{
									Name:  "ARES_ECR_ENDPOINT",
									Value: getEnvWithDefault("ARES_ECR_ENDPOINT", "586518255992.dkr.ecr.ca-central-1.amazonaws.com/ares-pandora"),
								},
								// Add NAMESPACE for Ares sanitization
								{
									Name:  "NAMESPACE",
									Value: os.Getenv("POD_NAMESPACE"),
								},
								// Add TIME for Ares sanitization job naming using America/Edmonton timezone
								{
									Name:  "TIME",
									Value: utils.GetTimestampInTimezone(),
								},
							},
						},
					},
					RestartPolicy: corev1.RestartPolicyOnFailure,
				},
			},
		},
	}

	return k8sClient.BatchV1().Jobs(os.Getenv("POD_NAMESPACE")).Create(context.TODO(), job, metav1.CreateOptions{})
}
